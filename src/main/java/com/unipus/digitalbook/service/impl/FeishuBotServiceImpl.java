package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson2.JSON;
import com.unipus.digitalbook.conf.feishu.FeishuBotProperties;
import com.unipus.digitalbook.model.dto.feishu.FeishuCardMessage;
import com.unipus.digitalbook.model.dto.feishu.FeishuMessage;
import com.unipus.digitalbook.model.dto.feishu.FeishuPostMessage;
import com.unipus.digitalbook.model.dto.feishu.FeishuTextMessage;
import com.unipus.digitalbook.service.FeishuBotService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 飞书机器人服务实现类
 */
@Slf4j
@Service
public class FeishuBotServiceImpl implements FeishuBotService {
    
    private static final String MARKDOWN_TAG = "markdown";
    private static final String TEXT_TAG = "text";
    private static final String POST_TAG = "post";
    private static final String INTERACTIVE_TAG = "interactive";
    private static final String PLAIN_TEXT_TAG = "plain_text";
    private static final String BUTTON_TAG = "button";
    private static final String HR_TAG = "hr";
    private static final String STANDARD_ICON_TAG = "standard_icon";
    private static final String LINK_TAG = "a";
    
    // 样式常量
    private static final String ALIGN_LEFT = "left";
    private static final String ALIGN_CENTER = "center";
    private static final String ALIGN_TOP = "top";
    private static final String DIRECTION_VERTICAL = "vertical";
    private static final String POSITION_TOP = "top";
    private static final String WIDTH_AUTO = "auto";
    private static final String COLOR_GREY = "grey";
    private static final String COLOR_BLUE = "blue";
    
    // 间距常量
    private static final String MARGIN_NONE = "0px 0px 0px 0px";
    private static final String MARGIN_MEDIUM = "8px 0px 0px 0px";
    private static final String MARGIN_LARGE = "8px 8px 8px 8px";
    private static final String PADDING_HEADER = "4px 0px 4px 8px";
    private static final String PADDING_PANEL = "8px 8px 8px 8px";
    private static final String SPACING_8PX = "8px";
    private static final String CORNER_RADIUS = "5px";
    private static final String ICON_SIZE = "16px 16px";
    
    // UI元素常量
    private static final String TEXT_SIZE_NORMAL = "normal_v2";
    private static final String BUTTON_TYPE_PRIMARY = "primary_filled";
    private static final String BUTTON_WIDTH_FILL = "fill";
    private static final String BUTTON_SIZE_LARGE = "large";
    private static final String ICON_TOKEN_SEARCH = "search";
    private static final String ICON_TOKEN_INFO = "info";
    private static final String BEHAVIOR_TYPE_OPEN_URL = "open_url";
    private static final String ICON_POSITION_FOLLOW_TEXT = "follow_text";
    
    // 文本常量
    private static final String ERROR_NOTIFICATION_TITLE = "❌ 系统错误通知";
    private static final String VIEW_DETAILED_LOGS = "查看详细日志";
    private static final String CONTEXT_INFO_HEADER = "**📋 上下文信息 >点击查看详情**";
    private static final String SEND_TIME_PREFIX = "发送时间：";
    private static final String OCCURRENCE_TIME_PREFIX = "发生时间：";
    private static final String ERROR_INFO_PREFIX = "**错误信息：**\n";
    private static final String CONTEXT_INFO_PREFIX = "**上下文信息：**\n";
    private static final String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    
    @Resource
    private FeishuBotProperties feishuBotProperties;
    
    @Resource
    private RestTemplate restTemplate;

    @Override
    public boolean sendTextMessage(String text) {
        FeishuTextMessage textMessage = new FeishuTextMessage();
        textMessage.setText(text);
        return sendTextMessage(textMessage);
    }
    
    @Override
    public boolean sendTextMessage(FeishuTextMessage textMessage) {
        try {
            FeishuMessage message = new FeishuMessage();
            message.setMsgType(TEXT_TAG);
            message.setContent(textMessage);
            
            return sendMessage(message);
        } catch (Exception e) {
            log.error("发送文本消息失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendPostMessage(FeishuPostMessage postMessage) {
        try {
            // 使用正确的post消息格式
            FeishuMessage message = new FeishuMessage();
            message.setMsgType(POST_TAG);
            Map<String, Object> content = new HashMap<>();
            content.put(POST_TAG, postMessage.getPost());
            message.setContent(content);
            
            return sendMessage(message);
        } catch (Exception e) {
            log.error("发送富文本消息失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendCardMessage(FeishuCardMessage cardMessage) {
        try {
            FeishuMessage message = new FeishuMessage();
            message.setMsgType(INTERACTIVE_TAG);
            // 卡片消息直接使用整个cardMessage对象
            message.setCard(cardMessage);
            
            return sendMessage(message);
        } catch (Exception e) {
            log.error("发送卡片消息失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendCustomMessage(String messageType, Object content) {
        try {
            FeishuMessage message = new FeishuMessage();
            message.setMsgType(messageType);
            message.setContent(content);
            
            return sendMessage(message);
        } catch (Exception e) {
            log.error("发送自定义消息失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendNotification(String title, String content) {
        try {
            // 创建卡片消息
            FeishuCardMessage cardMessage = new FeishuCardMessage();
            
            // 设置卡片头部
            cardMessage.setHeader(createCardHeader(title));
            
            // 设置卡片主体
            FeishuCardMessage.CardBody body = new FeishuCardMessage.CardBody();
            List<Object> elements = new ArrayList<>();
            
            // 添加内容元素
            elements.add(createMarkdownElement(content, ALIGN_LEFT, MARGIN_NONE));
            
            // 添加时间元素
            elements.add(createHorizontalRuleElement(MARGIN_MEDIUM));
            elements.add(createTimeElement(SEND_TIME_PREFIX, MARGIN_MEDIUM));
            
            body.setElements(elements);
            cardMessage.setBody(body);
            
            return sendCardMessage(cardMessage);
        } catch (Exception e) {
            log.error("发送通知消息失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendErrorNotification(String error, String context) {
        try {
            // 创建错误通知卡片
            FeishuCardMessage cardMessage = new FeishuCardMessage();
            
            // 设置卡片头部
            cardMessage.setHeader(createCardHeader(ERROR_NOTIFICATION_TITLE));
            
            // 设置卡片主体
            FeishuCardMessage.CardBody body = new FeishuCardMessage.CardBody();
            List<Object> elements = new ArrayList<>();
            
            // 添加错误信息元素
            elements.add(createMarkdownElement(ERROR_INFO_PREFIX + error, ALIGN_LEFT, MARGIN_NONE));
            
            // 添加上下文信息元素
            if (context != null && !context.isEmpty()) {
                elements.add(createMarkdownElement(CONTEXT_INFO_PREFIX + context, ALIGN_LEFT, MARGIN_MEDIUM));
            }
            
            // 添加时间元素
            elements.add(createHorizontalRuleElement(MARGIN_MEDIUM));
            elements.add(createTimeElement(OCCURRENCE_TIME_PREFIX, MARGIN_MEDIUM));
            
            body.setElements(elements);
            cardMessage.setBody(body);
            
            return sendCardMessage(cardMessage);
        } catch (Exception e) {
            log.error("发送错误通知失败", e);
            return false;
        }
    }
    
    @Override
    public boolean sendErrorNotificationWithGrafanaButton(String error, String context, String requestId) {
        try {
            // 创建错误通知卡片
            FeishuCardMessage cardMessage = new FeishuCardMessage();
            
            // 设置卡片头部
            cardMessage.setHeader(createCardHeader(ERROR_NOTIFICATION_TITLE));
            
            // 设置卡片主体
            FeishuCardMessage.CardBody body = new FeishuCardMessage.CardBody();
            List<Object> elements = new ArrayList<>();
            
            // 添加错误信息元素
            elements.add(createMarkdownElement("**错误信息：**\n ```\n" + error + "\n```", ALIGN_LEFT, MARGIN_NONE));
            
            // 添加上下文信息元素（使用折叠面板）
            if (context != null && !context.isEmpty()) {
                elements.add(createContextCollapsiblePanel(context));
            }
            
            // 添加Grafana链接（如果有requestId）
            if (requestId != null && !requestId.isEmpty() && !"-".equals(requestId)) {
                elements.add(createGrafanaButton(requestId));
            }
            
            body.setElements(elements);
            cardMessage.setBody(body);
            
            // 添加调试日志
            log.info("生成的卡片消息JSON: {}", JSON.toJSONString(cardMessage));
            
            return sendCardMessage(cardMessage);
        } catch (Exception e) {
            log.error("发送带Grafana按钮的错误通知失败", e);
            return false;
        }
    }

    /**
     * 创建卡片头部
     *
     * @param title 标题内容
     * @return 卡片头部对象
     */
    private FeishuCardMessage.CardHeader createCardHeader(String title) {
        FeishuCardMessage.CardHeader header = new FeishuCardMessage.CardHeader();
        FeishuCardMessage.CardTitle cardTitle = new FeishuCardMessage.CardTitle();
        cardTitle.setContent(title);
        cardTitle.setTag(PLAIN_TEXT_TAG);
        header.setTitle(cardTitle);
        return header;
    }

    /**
     * 创建Markdown元素
     *
     * @param content 内容
     * @param textAlign 文本对齐方式
     * @param margin 边距
     * @return Markdown元素
     */
    private FeishuCardMessage.CardElement createMarkdownElement(String content, String textAlign, String margin) {
        FeishuCardMessage.CardElement element = new FeishuCardMessage.CardElement();
        element.setTag(MARKDOWN_TAG);
        element.setContent(content);
        element.setTextAlign(textAlign);
        element.setTextSize(TEXT_SIZE_NORMAL);
        element.setMargin(margin);
        return element;
    }

    /**
     * 创建水平分割线元素
     *
     * @param margin 边距
     * @return 水平分割线元素
     */
    private FeishuCardMessage.CardElement createHorizontalRuleElement(String margin) {
        FeishuCardMessage.CardElement element = new FeishuCardMessage.CardElement();
        element.setTag(HR_TAG);
        element.setMargin(margin);
        return element;
    }

    /**
     * 创建时间元素
     *
     * @param timePrefix 时间前缀
     * @param margin 边距
     * @return 时间元素
     */
    private FeishuCardMessage.CardElement createTimeElement(String timePrefix, String margin) {
        FeishuCardMessage.CardElement element = new FeishuCardMessage.CardElement();
        element.setTag(MARKDOWN_TAG);
        element.setContent(timePrefix + LocalDateTime.now().format(DateTimeFormatter.ofPattern(DATE_TIME_PATTERN)));
        element.setTextAlign(ALIGN_LEFT);
        element.setMargin(margin);
        return element;
    }

    /**
     * 创建上下文信息折叠面板
     *
     * @param context 上下文内容
     * @return 折叠面板对象
     */
    private FeishuCardMessage.CollapsiblePanel createContextCollapsiblePanel(String context) {
        FeishuCardMessage.CollapsiblePanel collapsiblePanel = new FeishuCardMessage.CollapsiblePanel();
        collapsiblePanel.setElementId("context_info_panel");
        collapsiblePanel.setDirection(DIRECTION_VERTICAL);
        collapsiblePanel.setVerticalSpacing(SPACING_8PX);
        collapsiblePanel.setHorizontalSpacing(SPACING_8PX);
        collapsiblePanel.setVerticalAlign(ALIGN_TOP);
        collapsiblePanel.setHorizontalAlign(ALIGN_LEFT);
        collapsiblePanel.setPadding(PADDING_PANEL);
        collapsiblePanel.setMargin(MARGIN_MEDIUM);
        collapsiblePanel.setExpanded(false);
        collapsiblePanel.setBackgroundColor(COLOR_GREY);

        // 设置折叠面板头部
        collapsiblePanel.setHeader(createCollapsibleHeader(CONTEXT_INFO_HEADER));

        // 设置边框
        collapsiblePanel.setBorder(createCollapsibleBorder());

        // 设置面板内容
        List<Object> panelElements = new ArrayList<>();
        panelElements.add(createMarkdownElement(context, ALIGN_LEFT, MARGIN_NONE));
        collapsiblePanel.setElements(panelElements);
        
        return collapsiblePanel;
    }

    /**
     * 创建折叠面板头部
     *
     * @param title 标题内容
     * @return 折叠面板头部对象
     */
    private FeishuCardMessage.CollapsibleHeader createCollapsibleHeader(String title) {
        FeishuCardMessage.CollapsibleHeader header = new FeishuCardMessage.CollapsibleHeader();
        FeishuCardMessage.CardTitle headerTitle = new FeishuCardMessage.CardTitle();
        headerTitle.setTag(MARKDOWN_TAG);
        headerTitle.setContent(title);
        header.setTitle(headerTitle);
        header.setBackgroundColor(COLOR_GREY);
        header.setVerticalAlign(ALIGN_CENTER);
        header.setPadding(PADDING_HEADER);
        header.setPosition(POSITION_TOP);
        header.setWidth(WIDTH_AUTO);

        // 设置头部图标
        header.setIcon(createInfoIcon());
        header.setIconPosition(ICON_POSITION_FOLLOW_TEXT);
        header.setIconExpandedAngle(-180);

        return header;
    }

    /**
     * 创建信息图标
     *
     * @return 图标对象
     */
    private FeishuCardMessage.CardIcon createInfoIcon() {
        FeishuCardMessage.CardIcon icon = new FeishuCardMessage.CardIcon();
        icon.setTag(STANDARD_ICON_TAG);
        icon.setToken(ICON_TOKEN_INFO);
        icon.setColor(COLOR_BLUE);
        icon.setSize(ICON_SIZE);
        return icon;
    }

    /**
     * 创建折叠面板边框
     *
     * @return 边框对象
     */
    private FeishuCardMessage.CollapsibleBorder createCollapsibleBorder() {
        FeishuCardMessage.CollapsibleBorder border = new FeishuCardMessage.CollapsibleBorder();
        border.setColor(COLOR_GREY);
        border.setCornerRadius(CORNER_RADIUS);
        return border;
    }

    /**
     * 创建Grafana按钮
     *
     * @param requestId 请求ID
     * @return 按钮元素
     */
    private FeishuCardMessage.CardElement createGrafanaButton(String requestId) {
        FeishuCardMessage.CardElement buttonElement = new FeishuCardMessage.CardElement();
        buttonElement.setTag(BUTTON_TAG);
        
        // 按钮文本
        FeishuCardMessage.CardText buttonText = new FeishuCardMessage.CardText();
        buttonText.setTag(PLAIN_TEXT_TAG);
        buttonText.setContent(VIEW_DETAILED_LOGS);
        buttonElement.setText(buttonText);
        
        // 按钮属性
        buttonElement.setType(BUTTON_TYPE_PRIMARY);
        buttonElement.setWidth(BUTTON_WIDTH_FILL);
        buttonElement.setSize(BUTTON_SIZE_LARGE);
        
        // 按钮图标
        buttonElement.setIcon(createSearchIcon());
        
        // 按钮行为
        buttonElement.setBehaviors(createButtonBehaviors(requestId));
        
        buttonElement.setMargin(MARGIN_LARGE);
        return buttonElement;
    }

    /**
     * 创建搜索图标
     *
     * @return 搜索图标对象
     */
    private FeishuCardMessage.CardIcon createSearchIcon() {
        FeishuCardMessage.CardIcon icon = new FeishuCardMessage.CardIcon();
        icon.setTag(STANDARD_ICON_TAG);
        icon.setToken(ICON_TOKEN_SEARCH);
        return icon;
    }

    /**
     * 创建按钮行为
     *
     * @param requestId 请求ID
     * @return 行为列表
     */
    private List<FeishuCardMessage.CardBehavior> createButtonBehaviors(String requestId) {
        List<FeishuCardMessage.CardBehavior> behaviors = new ArrayList<>();
        FeishuCardMessage.CardBehavior behavior = new FeishuCardMessage.CardBehavior();
        behavior.setType(BEHAVIOR_TYPE_OPEN_URL);
        behavior.setDefaultUrl(buildGrafanaUrl(requestId));
        behavior.setPcUrl("");
        behavior.setIosUrl("");
        behavior.setAndroidUrl("");
        behaviors.add(behavior);
        return behaviors;
    }

    /**
     * 构建Grafana搜索链接
     *
     * @param requestId 请求ID
     * @return Grafana搜索链接
     */
    private String buildGrafanaUrl(String requestId) {
        String baseUrl = feishuBotProperties.getGrafanaBaseUrl();
        String encodedRequestId = java.net.URLEncoder.encode(requestId, StandardCharsets.UTF_8);
        
        // 构建Grafana Explore页面URL，使用配置化的参数
        return String.format("%s/explore?orgId=1&left={\"datasource\":\"%s\",\"queries\":[{\"refId\":\"A\",\"expr\":\"{app=\\\"%s\\\"}|=`%s`\",\"queryType\":\"range\",\"datasource\":{\"type\":\"loki\",\"uid\":\"%s\"},\"editorMode\":\"builder\"}],\"range\":{\"from\":\"%s\",\"to\":\"%s\"}}",
                baseUrl, 
                feishuBotProperties.getGrafanaDatasourceUid(), 
                feishuBotProperties.getGrafanaAppName(), 
                encodedRequestId, 
                feishuBotProperties.getGrafanaDatasourceUid(), 
                feishuBotProperties.getGrafanaTimeRange(), 
                feishuBotProperties.getGrafanaTimeRangeTo());
    }
    
    /**
     * 发送消息到飞书机器人
     */
    private boolean sendMessage(FeishuMessage message) {
        if (!feishuBotProperties.isEnabled()) {
            log.warn("飞书机器人未启用");
            return false;
        }
        
        if (feishuBotProperties.getWebhookUrl() == null || feishuBotProperties.getWebhookUrl().isEmpty()) {
            log.error("飞书机器人Webhook地址未配置");
            return false;
        }
        
        try {
            // 添加签名
            if (feishuBotProperties.getSecret() != null && !feishuBotProperties.getSecret().isEmpty()) {
                addSignature(message);
            }
            
            // 发送请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            log.debug("飞书发送请求：{}", JSON.toJSONString(message) );
            
            HttpEntity<String> request = new HttpEntity<>(JSON.toJSONString(message), headers);
            
            ResponseEntity<String> response = restTemplate.exchange(
                feishuBotProperties.getWebhookUrl(),
                HttpMethod.POST,
                request,
                String.class
            );
            
            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("飞书消息发送成功");
                return true;
            } else {
                log.error("飞书消息发送失败，状态码：{}，响应：{}", response.getStatusCode(), response.getBody());
                return false;
            }
        } catch (Exception e) {
            log.error("发送飞书消息异常： {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 添加消息签名
     */
    private void addSignature(FeishuMessage message) {
        try {
            long timestamp = System.currentTimeMillis() / 1000;
            String stringToSign = timestamp + "\n" + feishuBotProperties.getSecret();
            
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(feishuBotProperties.getSecret().getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            
            byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            String sign = Base64.getEncoder().encodeToString(signData);
            
            message.setTimestamp(timestamp);
            message.setSign(sign);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("生成消息签名失败", e);
        }
    }

    /**
     * 构建简单的富文本消息
     *
     * @param title 标题
     * @param content 内容
     * @return 富文本消息对象
     */
    public FeishuPostMessage buildSimplePostMessage(String title, String content) {
        FeishuPostMessage postMessage = new FeishuPostMessage();
        FeishuPostMessage.PostContent postContent = new FeishuPostMessage.PostContent();
        FeishuPostMessage.PostContentDetail detail = new FeishuPostMessage.PostContentDetail();
        
        // 设置标题
        detail.setTitle(title);
        
        // 构建二维数组格式的内容
        List<List<FeishuPostMessage.ContentItem>> contentList = getContentList(title, content);

        detail.setContent(contentList);
        postContent.setZh_cn(detail);
        postMessage.setPost(postContent);
        
        return postMessage;
    }

    @NotNull
    private static List<List<FeishuPostMessage.ContentItem>> getContentList(String title, String content) {
        List<List<FeishuPostMessage.ContentItem>> contentList = new ArrayList<>();

        // 第一行：标题和内容在同一行
        List<FeishuPostMessage.ContentItem> firstRow = new ArrayList<>();
        FeishuPostMessage.ContentItem titleItem = new FeishuPostMessage.ContentItem();
        titleItem.setTag(TEXT_TAG);
        titleItem.setText(title + ": ");
        firstRow.add(titleItem);

        FeishuPostMessage.ContentItem contentItem = new FeishuPostMessage.ContentItem();
        contentItem.setTag(TEXT_TAG);
        contentItem.setText(content);
        firstRow.add(contentItem);
        contentList.add(firstRow);
        return contentList;
    }

    /**
     * 构建带链接的富文本消息
     *
     * @param title 标题
     * @param content 内容
     * @param linkText 链接文本
     * @param linkUrl 链接地址
     * @return 富文本消息对象
     */
    public FeishuPostMessage buildPostMessageWithLink(String title, String content, String linkText, String linkUrl) {
        FeishuPostMessage postMessage = new FeishuPostMessage();
        FeishuPostMessage.PostContent postContent = new FeishuPostMessage.PostContent();
        FeishuPostMessage.PostContentDetail detail = new FeishuPostMessage.PostContentDetail();
        
        // 设置标题
        detail.setTitle(title);
        
        // 构建二维数组格式的内容
        List<List<FeishuPostMessage.ContentItem>> contentList = getContentList(content, linkText, linkUrl);

        detail.setContent(contentList);
        postContent.setZh_cn(detail);
        postMessage.setPost(postContent);
        
        return postMessage;
    }

    @NotNull
    private static List<List<FeishuPostMessage.ContentItem>> getContentList(String content, String linkText, String linkUrl) {
        List<List<FeishuPostMessage.ContentItem>> contentList = new ArrayList<>();

        // 第一行：文本和链接在同一行
        List<FeishuPostMessage.ContentItem> firstRow = new ArrayList<>();
        FeishuPostMessage.ContentItem textItem = new FeishuPostMessage.ContentItem();
        textItem.setTag(TEXT_TAG);
        textItem.setText(content + " ");
        firstRow.add(textItem);

        FeishuPostMessage.ContentItem linkItem = new FeishuPostMessage.ContentItem();
        linkItem.setTag(LINK_TAG);
        linkItem.setText(linkText);
        linkItem.setHref(linkUrl);
        firstRow.add(linkItem);
        contentList.add(firstRow);
        return contentList;
    }

    /**
     * 构建带折叠面板的卡片消息
     *
     * @param title 卡片标题
     * @param panelTitle 折叠面板标题
     * @param panelContent 折叠面板内容
     * @param expanded 是否默认展开
     * @return 卡片消息对象
     */
    public FeishuCardMessage buildCardMessageWithCollapsiblePanel(String title, String panelTitle, String panelContent, boolean expanded) {
        FeishuCardMessage cardMessage = new FeishuCardMessage();
        
        // 设置卡片头部
        cardMessage.setHeader(createCardHeader(title));
        
        // 设置卡片主体
        FeishuCardMessage.CardBody body = new FeishuCardMessage.CardBody();
        List<Object> elements = new ArrayList<>();
        
        // 创建折叠面板
        FeishuCardMessage.CollapsiblePanel collapsiblePanel = createCustomCollapsiblePanel(panelTitle, panelContent, expanded);
        
        // 将折叠面板添加到主元素列表
        elements.add(collapsiblePanel);
        
        body.setElements(elements);
        cardMessage.setBody(body);
        
        return cardMessage;
    }

    /**
     * 创建自定义折叠面板
     *
     * @param panelTitle 面板标题
     * @param panelContent 面板内容
     * @param expanded 是否默认展开
     * @return 折叠面板对象
     */
    private FeishuCardMessage.CollapsiblePanel createCustomCollapsiblePanel(String panelTitle, String panelContent, boolean expanded) {
        FeishuCardMessage.CollapsiblePanel collapsiblePanel = new FeishuCardMessage.CollapsiblePanel();
        collapsiblePanel.setElementId("custom_panel");
        collapsiblePanel.setDirection(DIRECTION_VERTICAL);
        collapsiblePanel.setVerticalSpacing(SPACING_8PX);
        collapsiblePanel.setHorizontalSpacing(SPACING_8PX);
        collapsiblePanel.setVerticalAlign(ALIGN_TOP);
        collapsiblePanel.setHorizontalAlign(ALIGN_LEFT);
        collapsiblePanel.setPadding(PADDING_PANEL);
        collapsiblePanel.setMargin(MARGIN_NONE);
        collapsiblePanel.setExpanded(expanded);
        collapsiblePanel.setBackgroundColor(COLOR_GREY);
        
        // 设置折叠面板头部
        collapsiblePanel.setHeader(createCollapsibleHeader("**" + panelTitle + "**"));
        
        // 设置边框
        collapsiblePanel.setBorder(createCollapsibleBorder());
        
        // 设置面板内容
        List<Object> panelElements = new ArrayList<>();
        panelElements.add(createMarkdownElement(panelContent, ALIGN_LEFT, MARGIN_NONE));
        collapsiblePanel.setElements(panelElements);
        
        return collapsiblePanel;
    }
} 