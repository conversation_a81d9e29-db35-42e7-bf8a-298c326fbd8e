package com.unipus.digitalbook.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.unipus.digitalbook.common.exception.assistant.AssistantException;
import com.unipus.digitalbook.common.utils.ApplicationContextUtil;
import com.unipus.digitalbook.common.utils.SHA256Util;
import com.unipus.digitalbook.dao.AssistantPOMapper;
import com.unipus.digitalbook.dao.AssistantVersionPOMapper;
import com.unipus.digitalbook.dao.ChapterVersionPOMapper;
import com.unipus.digitalbook.model.common.Response;
import com.unipus.digitalbook.model.dto.assistant.*;
import com.unipus.digitalbook.model.entity.UserInfo;
import com.unipus.digitalbook.model.entity.book.Book;
import com.unipus.digitalbook.model.entity.chapter.Chapter;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import com.unipus.digitalbook.model.entity.chapter.ChapterNodeStructure;
import com.unipus.digitalbook.model.enums.AssistantConfigTypeEnum;
import com.unipus.digitalbook.model.enums.AssistantTemplateStatusEnum;
import com.unipus.digitalbook.model.enums.QuestionGroupTypeEnum;
import com.unipus.digitalbook.model.params.assistant.AssistantAddParam;
import com.unipus.digitalbook.model.params.assistant.AssistantSelectBlocksParam;
import com.unipus.digitalbook.model.params.assistant.AssistantTemplateReferenceParam;
import com.unipus.digitalbook.model.po.assistant.AssistantPO;
import com.unipus.digitalbook.model.po.assistant.AssistantVersionPO;
import com.unipus.digitalbook.model.po.book.BookSettingPO;
import com.unipus.digitalbook.model.po.chapter.ChapterVersionPO;
import com.unipus.digitalbook.service.*;
import com.unipus.digitalbook.service.remote.restful.assistant.AssistantApiService;
import com.unipus.digitalbook.service.remote.restful.ucontent.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AssistantServiceImpl implements AssistantService {

    @Value("${remote.assistant.appKey}")
    private String appKey;

    @Value("${remote.assistant.appSecret}")
    private String appSecret;

    @Resource
    private AssistantApiService assistantApiService;

    @Resource
    private AssistantPOMapper assistantPOMapper;

    @Resource
    private AssistantVersionPOMapper assistantVersionPOMapper;

    @Resource
    private UserService userService;

    @Resource
    private BookVersionService bookVersionService;

    @Resource
    private ChapterVersionPOMapper chapterVersionPOMapper;

    @Resource
    private LoadingCache<Long, ChapterNodeStructure> chapterNodeCache;

    @Resource
    private BookService bookService;

    @Resource
    private ChapterService chapterService;

    private String assistantToken(Long currentUserId) {
        UserInfo userInfo = userService.getUserInfo(currentUserId);
        Objects.requireNonNull(userInfo, "用户不存在");
        String openId = userInfo.getSsoId();
        return assistantToken(openId);
    }

    private String assistantToken(String openId) {
        long timestamp = System.currentTimeMillis();
        String nonce = UUID.randomUUID().toString();
        String origin = appKey + "-" + appSecret+ "-" + timestamp + "-" + nonce + "-" + openId;
        String signature = SHA256Util.hash(origin).toUpperCase();
        AssistantTokenRequest request = new AssistantTokenRequest
                (appKey, openId, timestamp, nonce, signature);
        try {
            BaseResponse<String> response = assistantApiService.assistantToken(request);
            int code = response.getCode();
            if (code == 0) {
                return response.getData();
            }
            log.error("openId {} get assistantToken errorCode {}", openId, code);
            throw new AssistantException("获取数字人token异常");
        } catch (Exception e) {
            log.error("openId {} get assistantToken error", openId, e);
            throw new AssistantException("获取数字人token异常", e);
        }
    }

    @Override
    public Response<AssistantPositionInfoListDTO> latestInstanceList(String openId, String bookId, String bookVersionNumber, String chapterId) {

        AssistantPositionInfoListDTO assistantPositionInfoListDTO = new AssistantPositionInfoListDTO();
        BookSettingService bookSettingService = ApplicationContextUtil.getBean(BookSettingService.class);
        assistantPositionInfoListDTO.setGreeting(bookSettingService.greeting(bookId));
        List<AssistantVersionPO> assistantVersionPOS = findLatestVersionByBookIdAndChapterId(bookId, chapterId);
        if (CollectionUtils.isEmpty(assistantVersionPOS)) {
            log.warn("没有找到数字人，bookId:{} chapterId:{}", bookId, chapterId);
            assistantPositionInfoListDTO.setList(Lists.newArrayList());
            return Response.success(assistantPositionInfoListDTO);
        }

        Map<String, List<String>> assistantIdToBlockIds = assistantVersionPOS.stream()
                .collect(Collectors.toMap(AssistantVersionPO::getCmsId, po -> JSONArray.parseArray(po.getBlockIds(), String.class)));
        List<String> assistantIds = assistantVersionPOS.stream().map(AssistantVersionPO::getId).toList();
        AssistantLatestInstanceListRequest request = new AssistantLatestInstanceListRequest(bookId, assistantIds);
        BaseResponse<List<AssistantLatestInstanceResponse>> response = assistantApiService.latestInstanceList(assistantToken(openId), request);
        if (response.getCode() == 0) {
            List<AssistantPositionInfoDTO> list = response.getData().stream().map(d ->
                    new AssistantPositionInfoDTO(d, assistantIdToBlockIds.get(d.getAssistantId()))).toList();

            assistantPositionInfoListDTO.setList(list.stream()
                    .filter(dto -> !"close".equalsIgnoreCase(dto.getBusinessFlag())).toList());
            return Response.success(assistantPositionInfoListDTO);
        } else {
            log.error("查询数字人最新实例列表失败，bookId:{} assistantIds:{} 错误码：{}", bookId, String.join(",", assistantIds), response.getCode());
            return Response.fail(response.getCode(), response.getMessage());
        }
    }

    @Override
    public Response<AssistantInfoListDTO> instanceList(Long currentUserId, String bookId, String chapterId) {
        List<AssistantPO> assistantPOS = findByBookIdAndChapterId(bookId, chapterId);
        AssistantInfoListDTO assistantInfoListDTO = new AssistantInfoListDTO();
        List<AssistantDTO> list = assistantPOS.stream().map(AssistantDTO::fromPO).toList();
        fillUserInfo(list);
        fillTemplateInfo(list);
        assistantInfoListDTO.setList(list);

        return Response.success(assistantInfoListDTO);
    }

    private void fillUserInfo(List<AssistantDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> userIds = list.stream().map(AssistantDTO::getUpdateBy).distinct().toList();
        Map<Long, String> usernameMap = userService.getUserNames(userIds);
        list.forEach(d -> d.setUsername(usernameMap.get(d.getUpdateBy())));
    }

    private void fillTemplateInfo(List<AssistantDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        Map<String, Response<AssistantTemplateResponse>> tempalteMap = Maps.newHashMap();
        list.forEach(assistantDTO -> {
            String templateId = assistantDTO.getTemplateId();
            if (templateId == null) {
                return;
            }

            Response<AssistantTemplateResponse> response;
            if (tempalteMap.containsKey(templateId)) {
                response = tempalteMap.get(templateId);
            } else {
                response = queryTemplate(assistantDTO.getCreateBy(), templateId);
                tempalteMap.put(templateId, response);
            }
            if (Boolean.TRUE.equals(response.isSuccess())) {
                AssistantTemplateResponse templateResponse = response.getData();
                if (templateResponse == null) {
                    return;
                }
                assistantDTO.setTemplateName(templateResponse.getTitle());
                assistantDTO.setTemplateValid(Objects.equals(templateResponse.getStatus(), AssistantTemplateStatusEnum.ENABLE.getCode()));

                if (Objects.equals(assistantDTO.getConfigType(), AssistantConfigTypeEnum.TEMPLATE.getCode())) {
                    assistantDTO.setTemplateJson(templateResponse.getConfig());
                    assistantDTO.setType(templateResponse.getAssistantType());
                    assistantDTO.setTemplateName(templateResponse.getTitle());
                }
            }
        });
    }

    @Override
    public BaseResponse<Void> publishAll(Long currentUserId, String bookId) {
        AssistantPublishAllRequest request = new AssistantPublishAllRequest(bookId);
        return assistantApiService.publishAll(assistantToken(currentUserId), request);
    }

    @Override
    public void addAssistant(AssistantAddParam param, Long currentUserId) {
        AssistantPO assistantPO = param.toEntity(currentUserId);
        String bookId = assistantPO.getBookId();
        String chapterId = assistantPO.getChapterId();
        List<String> allBlockIds = findByBookIdAndChapterId(bookId, chapterId)
                .stream().map(po -> JSONArray.parseArray(po.getBlockIds(), String.class))
                .flatMap(List::stream)
                .collect(Collectors.toSet()).stream().toList();
        List<String> blockIds = JSONArray.parseArray(assistantPO.getBlockIds(), String.class);
        if (blockIds.stream().anyMatch(allBlockIds::contains)) {
            throw new AssistantException("所选内容已关联标注，请刷新画面后重新操作");
        }

        AssistantSaveRequest request = new AssistantSaveRequest();
        request.setAssistantType(assistantPO.getType());
        request.setConfig(assistantPO.getTemplateJson());
        String title = assistantPO.getName();
        if (StringUtils.hasText(title)) {
            request.setTitle(title);
        } else {
            request.setTitle("标题");
        }
        request.setScene(bookId);
        request.setConfigType(assistantPO.getConfigType());
        request.setEntityId(assistantPO.getId());
        request.setTemplateId(assistantPO.getTemplateId());
        BaseResponse<AssistantSaveResponse> response = assistantApiService.saveAssistant(assistantToken(assistantPO.getCreateBy()), request);
        if (response.getCode() != 0) {
            log.error("数字人保存失败，错误码：{}", response.getCode());
            throw new AssistantException("数字人保存失败");
        }
        assistantPO.setCmsId(response.getData().getAssistantId());
        assistantPOMapper.upsertAssistant(assistantPO);
    }

    @Override
    public void updateAssistant(AssistantPO assistantPO) {
        AssistantSaveRequest request = new AssistantSaveRequest();
        request.setAssistantType(assistantPO.getType());
        request.setConfig(assistantPO.getTemplateJson());
        String title = assistantPO.getName();
        if (StringUtils.hasText(title)) {
            request.setTitle(title);
        } else {
            request.setTitle("标题");
        }
        request.setScene(assistantPO.getBookId());
        request.setConfigType(assistantPO.getConfigType());
        request.setEntityId(assistantPO.getId());
        request.setTemplateId(assistantPO.getTemplateId());
        BaseResponse<AssistantSaveResponse> response = assistantApiService.saveAssistant(assistantToken(assistantPO.getCreateBy()), request);
        if (response.getCode() != 0) {
            log.error("数字人更新失败，错误码：{}", response.getCode());
            throw new AssistantException("数字人保存失败");
        }
        assistantPO.setCmsId(response.getData().getAssistantId());
        assistantPOMapper.updateAssistant(assistantPO);
    }

    @Override
    public Response<AssistantDTO> queryAssistant(Long currentUserId, String assistantId) {
        AssistantDTO assistantDTO = AssistantDTO.fromPO(selectById(assistantId));
        fillTemplateInfo(List.of(assistantDTO));
        return Response.success(assistantDTO);
    }

    @Override
    public Response<AssistantTemplateSearchResponse> templateList(Long currentUserId, Integer assistantType) {
        AssistantTemplateSearchRequest request = new AssistantTemplateSearchRequest();
        request.setAssistantType(assistantType);
        BaseResponse<AssistantTemplateSearchResponse> response = assistantApiService.templateList(assistantToken(currentUserId), request);
        if (response.getCode() == 0) {
            return Response.success(response.getData());
        } else {
            log.error("查询数字人模板失败，assistantType:{} 错误码：{}", assistantType, response.getCode());
            return Response.fail(response.getCode(), response.getMessage());
        }
    }

    @Override
    public Response<AssistantTemplateResponse> queryTemplate(Long currentUserId, String templateId) {
        AssistantTemplateQueryRequest request = new AssistantTemplateQueryRequest(templateId);
        BaseResponse<AssistantTemplateResponse> response = assistantApiService.queryTemplate(assistantToken(currentUserId), request);
        if (response.getCode() == 0) {
            return Response.success(response.getData());
        } else {
            log.error("获取数字人模板失败，templateId:{} 错误码：{}", templateId, response.getCode());
            return Response.fail(response.getCode(), response.getMessage());
        }
    }

    private List<AssistantPO> findByBookIdAndChapterId(String bookId, String chapterId) {
        return assistantPOMapper.findByBookIdAndChapterId(bookId, chapterId);
    }

    @Override
    public void disableById(String id) {
        assistantPOMapper.disableById(id);
    }

    @Override
    public AssistantPO selectById(String id) {
        return assistantPOMapper.selectById(id);
    }

    @Override
    public Response<AssistantTypeListResponse> assistantTypeList(Long currentUserId) {
        BaseResponse<AssistantTypeListResponse> response = assistantApiService.assistantType(assistantToken(currentUserId));
        if (response.getCode() == 0) {
            return Response.success(response.getData());
        } else {
            log.error("获取助教类型失败，错误码：{}", response.getCode());
            return Response.fail(response.getCode(), response.getMessage());
        }
    }

    @Override
    public Response<String> selectBlocks(AssistantSelectBlocksParam param) {
        String bookId = param.getBookId();
        String chapterId = param.getChapterId();
        List<String> blockIds = param.getBlockIds();
        List<AssistantPO> assistantPOS = findByBookIdAndChapterId(bookId, chapterId);
        if (CollectionUtils.isEmpty(assistantPOS)) {
            return Response.success();
        }
        List<String> allIds = assistantPOS.stream()
                .map(po -> JSONArray.parseArray(po.getBlockIds(), String.class))
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .distinct().toList();
        boolean exists = blockIds.stream().anyMatch(allIds::contains);
        if (exists) {
            return Response.fail("所选内容已关联标注，请刷新画面后重新操作");
        }

        return Response.success();
    }

    @Override
    public void publishByBookId(String bookId, String version) {
        List<AssistantPO> assistantPOS = assistantPOMapper.findByBookId(bookId);
        if (CollectionUtils.isEmpty(assistantPOS)) {
            log.warn("没有找到数字人，bookId:{}", bookId);
            return;
        }
        assistantVersionPOMapper.batchSave(assistantPOS.stream()
                .map(assistantPO -> assistantPO.toVersion(version)).toList());
    }

    private List<AssistantVersionPO> findLatestVersionByBookIdAndChapterId(String bookId, String chapterId) {
        BookSettingPO bookSettingPO = ApplicationContextUtil.getBean(BookSettingService.class).selectByBookId(bookId);
        //数字人未开启
        if (bookSettingPO == null || !bookSettingPO.getAssistantOpened()) {
            return Lists.newArrayList();
        }

        //数字人未发布
        String assistantVersion = bookSettingPO.getAssistantVersion();
        if (!StringUtils.hasText(assistantVersion)) {
            return Lists.newArrayList();
        }

        return assistantVersionPOMapper.findByBookIdAndChapterIdAndVersion(bookId, chapterId, assistantVersion);
    }

    private Map<String, String> getQuesTypeInfo(Map<String, ChapterNode> map, List<String> blockIds) {
        Map<String, String> quesTypes = Maps.newHashMap();
        blockIds.forEach(b -> quesTypes.put(b, findQuesType(map, b)));
        return quesTypes;
    }

    private String findQuesType(Map<String, ChapterNode> map, String blockId) {
        String quesType = Strings.EMPTY;
        ChapterNode node = map.get(blockId);
        if (node == null) {
            return quesType;
        }

        if (!StringUtils.hasText(node.getQuestionType())) {
            return quesType;
        }

        return Optional.ofNullable(QuestionGroupTypeEnum.getEnumByName(node.getQuestionType()))
                .map(QuestionGroupTypeEnum::getDesc)
                .orElse(quesType);

    }

    @Override
    public AssistantTemplateReferenceListDTO templateReference(AssistantTemplateReferenceParam param) {
        AssistantTemplateReferenceListDTO result = new AssistantTemplateReferenceListDTO();
        Integer totalCount = assistantPOMapper.countByConfigTypeAndTemplateId
                (AssistantConfigTypeEnum.TEMPLATE.getCode(), param.getTemplateId());
        result.setTotalCount(totalCount);

        List<AssistantPO> assistantPOS = assistantPOMapper.findByConfigTypeAndTemplateId
                (AssistantConfigTypeEnum.TEMPLATE.getCode(), param.getTemplateId(), param.getPageParams());

        Map<String, String> bookIdToNameMap = Maps.newHashMap();
        assistantPOS.stream().map(AssistantPO::getBookId).distinct().forEach(bookId -> bookIdToNameMap.put(bookId, Optional.ofNullable(bookService.getBookById(bookId))
                .map(Book::getChineseName).orElse("")));

        Map<String, String> chapterIdToNameMap = Maps.newHashMap();
        assistantPOS.stream().map(AssistantPO::getChapterId).distinct().forEach(chapterId -> chapterIdToNameMap.put(chapterId, Optional.ofNullable(chapterService.getChapterById(chapterId))
                .map(Chapter::getName).orElse("")));

        Map<String, Long> chapterIdToVersionIdMap = Maps.newHashMap();
        assistantPOS.stream().map(AssistantPO::getChapterId).distinct().forEach(chapterId -> {
            ChapterVersionPO chapterVersionPO = chapterVersionPOMapper.selectLatestVersionByChapterId(chapterId);
            if (chapterVersionPO != null) {
                chapterIdToVersionIdMap.put(chapterId, chapterVersionPO.getId());
            }
        });

        List<AssistantTemplateReferenceDTO> templateReferences =
                assistantPOS.stream().map(assistantPO -> {
                    AssistantTemplateReferenceDTO dto = new AssistantTemplateReferenceDTO();
                    dto.setBookName(bookIdToNameMap.get(assistantPO.getBookId()));
                    String chapterId = assistantPO.getChapterId();
                    String chapterName = chapterIdToNameMap.get(chapterId);
                    dto.setChapterName(chapterName);
                    if (!chapterIdToVersionIdMap.containsKey(chapterId)) {
                        return dto;
                    }

                    Long chapterVersionId = chapterIdToVersionIdMap.get(chapterId);
                    Optional.ofNullable(chapterNodeCache.get(chapterVersionId)).ifPresent(structure -> {
                        List<String> blockIds = JSONArray.parseArray(assistantPO.getBlockIds(), String.class);
                        Map<String, String> quesTypes = getQuesTypeInfo(structure.getNodeMap(), blockIds);
                        dto.setQuestionTypes(quesTypes.values().stream().filter(Objects::nonNull).filter(StringUtils::hasText).toList());
                    });

                    return dto;
                }).toList();
        result.setTemplateReferences(templateReferences);
        return result;
    }

}
