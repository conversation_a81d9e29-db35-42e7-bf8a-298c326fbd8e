package com.unipus.digitalbook.service.impl;

import com.unipus.digitalbook.dao.PaperScoreTemplatePOMapper;
import com.unipus.digitalbook.dao.PaperScoreTemplateRelationPOMapper;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplate;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateList;
import com.unipus.digitalbook.model.entity.template.PaperScoreTemplateRelation;
import com.unipus.digitalbook.model.enums.PaperScoreTemplateTypeEnum;
import com.unipus.digitalbook.model.po.template.PaperScoreTemplatePO;
import com.unipus.digitalbook.model.po.template.PaperScoreTemplateRelationPO;
import com.unipus.digitalbook.service.PaperScoreTemplateRelationService;
import com.unipus.digitalbook.service.PaperScoreTemplateService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.unipus.digitalbook.conf.cache.CacheManagerConfig.CACHE_NAME_PAPER_SCORE_TEMPLATE;

@Slf4j
@Service
public class PaperScoreTemplateRelationServiceImpl implements PaperScoreTemplateRelationService {

    @Resource
    private PaperScoreTemplateRelationPOMapper paperScoreTemplateRelationPOMapper;

    @Resource
    private PaperScoreTemplatePOMapper paperScoreTemplatePOMapper;

    @Resource
    private PaperScoreTemplateService paperScoreTemplateService;

    @Override
    public boolean addTemplateRelation(List<PaperScoreTemplateRelation> paperScoreTemplateRelationList, Long createBy) {
        List<Long> paperScoreTemplateIdList = paperScoreTemplateRelationPOMapper.selectTemplateIdListByBookId(paperScoreTemplateRelationList.getFirst().getBookId());
        if (CollectionUtils.isNotEmpty(paperScoreTemplateIdList)) {
            throw new IllegalArgumentException("您已关联模板");
        }

        checkPaperScoreTemplate(paperScoreTemplateRelationList);

        List<PaperScoreTemplateRelationPO> paperScoreTemplateRelationPOList = paperScoreTemplateRelationList.stream().map(p -> {
            PaperScoreTemplateRelationPO paperScoreTemplateRelationPO = new PaperScoreTemplateRelationPO();
            paperScoreTemplateRelationPO.fromEntity(p);
            paperScoreTemplateRelationPO.setCreateBy(createBy);
            paperScoreTemplateRelationPO.setUpdateBy(createBy);
            return paperScoreTemplateRelationPO;
        }).collect(Collectors.toList());

        paperScoreTemplateRelationPOMapper.batchInsert(paperScoreTemplateRelationPOList);

        return true;
    }

    private void checkPaperScoreTemplate(List<PaperScoreTemplateRelation> paperScoreTemplateRelationList) {
        List<PaperScoreTemplatePO> paperScoreTemplatePOList = paperScoreTemplatePOMapper.selectByIdList(paperScoreTemplateRelationList.stream().map(PaperScoreTemplateRelation::getPaperScoreTemplateId).collect(Collectors.toList()));
        Map<Integer, List<PaperScoreTemplatePO>> listMap = paperScoreTemplatePOList.stream().collect(Collectors.groupingBy(PaperScoreTemplatePO::getType));
        if (paperScoreTemplateRelationList.size() != listMap.size()) {
            throw new IllegalArgumentException("模板数据或者类型重复");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean editTemplateRelation(List<PaperScoreTemplateRelation> paperScoreTemplateRelationList, Long createBy) {
        checkPaperScoreTemplate(paperScoreTemplateRelationList);

        paperScoreTemplateRelationPOMapper.batchDisable(paperScoreTemplateRelationList.getFirst().getBookId(), createBy);

        List<PaperScoreTemplateRelationPO> paperScoreTemplateRelationPOList = paperScoreTemplateRelationList.stream().map(p -> {
            PaperScoreTemplateRelationPO paperScoreTemplateRelationPO = new PaperScoreTemplateRelationPO();
            paperScoreTemplateRelationPO.fromEntity(p);
            paperScoreTemplateRelationPO.setCreateBy(createBy);
            paperScoreTemplateRelationPO.setUpdateBy(createBy);
            return paperScoreTemplateRelationPO;
        }).collect(Collectors.toList());

        paperScoreTemplateRelationPOMapper.batchInsert(paperScoreTemplateRelationPOList);

        return true;
    }

    @Override
    public PaperScoreTemplateList getTemplateRelationList(String bookId) {
        List<Long> templateIdList = paperScoreTemplateRelationPOMapper.selectTemplateIdListByBookId(bookId);
        List<PaperScoreTemplatePO> paperScoreTemplatePOList = null;
        if (CollectionUtils.isNotEmpty(templateIdList)) {
            paperScoreTemplatePOList = paperScoreTemplatePOMapper.selectByIdList(templateIdList);
        }

        return PaperScoreTemplateList.assemblyPaperScoreTemplateList(paperScoreTemplatePOList, null);
    }

    /**
     * 获取教材对应的试卷得分模板（指定类型，已发布）
     *
     * @param bookId            教材ID
     * @param scoreTemplateType 试卷得分模板类型
     * @return 试卷得分模板详情
     */
    @Cacheable(cacheNames = CACHE_NAME_PAPER_SCORE_TEMPLATE, key = "#bookId+'_'+#scoreTemplateType")
    @Override
    public PaperScoreTemplate getPaperScoreTemplate(String bookId, PaperScoreTemplateTypeEnum scoreTemplateType) {
        PaperScoreTemplateList templateRelationList = getTemplateRelationList(bookId);
        if (ObjectUtils.isEmpty(templateRelationList) || CollectionUtils.isEmpty(templateRelationList.getTemplateList())) {
            log.error("试卷评价模板不存在:教材未关联模板，教材ID:{}", bookId);
            throw new IllegalArgumentException("试卷评价模板不存在");
        }

        PaperScoreTemplate paperScoreTemplate = templateRelationList.getTemplateList().stream().filter(p -> scoreTemplateType.getCode().equals(p.getType())).findFirst().orElse(null);
        if (ObjectUtils.isEmpty(paperScoreTemplate)) {
            log.error("试卷评价模板不存在:教材ID:{},模板类类型:{}", bookId, scoreTemplateType.getDesc());
            throw new IllegalArgumentException("试卷评价模板不存在:");
        }

        //当前生效中的模板
        PaperScoreTemplatePO effectPaperScoreTemplatePO = paperScoreTemplatePOMapper.selectEffectTemplateByIdAndType(paperScoreTemplate.getId(), scoreTemplateType.getCode());
        if (ObjectUtils.isEmpty(effectPaperScoreTemplatePO)) {
            log.error("试卷评价模板不存在:教材ID:{},模板类类型:{},发布状态:已发布", bookId, scoreTemplateType.getDesc());
            throw new IllegalArgumentException("试卷评价模板不存在:");
        }

        // 获取试卷得分模板详情
        PaperScoreTemplate templateDetail = paperScoreTemplateService.getTemplateDetail(effectPaperScoreTemplatePO.getId(), true);
        if (ObjectUtils.isEmpty(templateDetail)) {
            log.error("试卷评价模板不存在:未取得模板详情，教材ID:{},模板类类型:{}", bookId, scoreTemplateType.getDesc());
            throw new IllegalArgumentException("试卷评价模板不存在");
        }
        return templateDetail;
    }
}
