package com.unipus.digitalbook.conf.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

@Configuration
@EnableCaching
public class CacheManagerConfig {

    public static final String CACHE_NAME_PAPER_VERSION = "paperVersion";
    // 卷题目列表
    public static final String CACHE_NAME_PAPER_QUESTION = "PaperService:questionGroup";
    // 题库题目列表
    public static final String CACHE_NAME_PAPER_BANK_QS = "ChallengePaperInstanceStrategy::questionBankList";
    // 试卷详情
    public static final String CACHE_NAME_PAPER_DETAIL = "PaperService:paperDetail";
    // 试卷题目标签
    public static final String CACHE_NAME_PAPER_TAGS = "PaperService:tagProcessor";
    // 试卷题评价
    public static final String CACHE_NAME_PAPER_SCORE_TEMPLATE = "paperScoreTemplate";
    // 试卷成绩批次信息
    public static final String CACHE_NAME_PAPER_SCORE_BATCH = "paperScoreBatch";
    // 试卷用户答案信息
    public static final String CACHE_NAME_PAPER_ANSWER = "paperUserAnswer";

    private static final List<String> CACHE_NAMES = List.of(
            CACHE_NAME_PAPER_VERSION,
            CACHE_NAME_PAPER_QUESTION,
            CACHE_NAME_PAPER_BANK_QS,
            CACHE_NAME_PAPER_DETAIL,
            CACHE_NAME_PAPER_TAGS,
            CACHE_NAME_PAPER_SCORE_TEMPLATE,
            CACHE_NAME_PAPER_SCORE_BATCH,
            CACHE_NAME_PAPER_ANSWER
    );

    // 访问后过期时间，实现自动续期
    private static final Duration ACCESS_EXPIRE_TIME = Duration.ofDays(1);
    // 写入后强制过期时间，防止数据过期
    private static final Duration FORCE_EXPIRE_TIME = Duration.ofDays(3);

    @Bean
    @Primary
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();

        // 配置默认的Caffeine实例
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(Duration.ofDays(1)));

        // 注册缓存对象 - 支持访问时自动续期和统计
        CACHE_NAMES.forEach(cacheName ->
            cacheManager.registerCustomCache(cacheName,
                Caffeine.newBuilder()
                        .maximumSize(1000)
                        .expireAfterAccess(ACCESS_EXPIRE_TIME)
                        .expireAfterWrite(FORCE_EXPIRE_TIME)
                        .recordStats() // 启用统计
                        .build()));

        return cacheManager;
    }

    public <T> T getLocalCacheData(String cacheName, String key) {
        if (key == null) {
            return null;
        }
        Cache cache = cacheManager().getCache(cacheName);
        if (cache == null) {
            return null;
        }
        Cache.ValueWrapper wrapper = cache.get(key);
        if (wrapper == null) {
            return null;
        }
        @SuppressWarnings("unchecked")
        T cachedData = (T) wrapper.get();
        return cachedData;
    }

    public void addLocalCacheData(String cacheName, String key, Object value){
        if(key==null || value==null || value instanceof List<?> dataList && dataList.isEmpty() ){
            return;
        }
        Objects.requireNonNull(cacheManager().getCache(cacheName)).put(key, value);
    }
}
