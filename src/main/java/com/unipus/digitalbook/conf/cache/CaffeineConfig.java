package com.unipus.digitalbook.conf.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.unipus.digitalbook.dao.BookVersionChapterVersionRelationPOMapper;
import com.unipus.digitalbook.dao.BookVersionPOMapper;
import com.unipus.digitalbook.dao.ChapterVersionPOMapper;
import com.unipus.digitalbook.dao.TenantChannelPOMapper;
import com.unipus.digitalbook.dao.TenantSubscribePOMapper;
import com.unipus.digitalbook.model.entity.book.BookID;
import com.unipus.digitalbook.model.entity.chapter.ChapterNode;
import com.unipus.digitalbook.model.entity.chapter.ChapterNodeStructure;
import com.unipus.digitalbook.model.entity.publish.BookVersion;
import com.unipus.digitalbook.model.entity.question.BigQuestionGroup;
import com.unipus.digitalbook.model.entity.question.ChoiceQuestionOption;
import com.unipus.digitalbook.model.entity.question.Question;
import com.unipus.digitalbook.model.entity.question.QuestionAnswer;
import com.unipus.digitalbook.model.entity.question.QuestionId;
import com.unipus.digitalbook.model.entity.question.QuestionText;
import com.unipus.digitalbook.model.entity.tenant.TenantSubscribeID;
import com.unipus.digitalbook.model.po.BookVersionChapterVersionRelationPO;
import com.unipus.digitalbook.model.po.chapter.ChapterNodePO;
import com.unipus.digitalbook.model.po.chapter.ChapterVersionPO;
import com.unipus.digitalbook.model.po.publish.BookVersionPO;
import com.unipus.digitalbook.model.po.tenant.TenantChannelPO;
import com.unipus.digitalbook.model.po.tenant.TenantSubscribePO;
import com.unipus.digitalbook.service.QuestionService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Configuration
public class CaffeineConfig {
    @Resource
    private ChapterVersionPOMapper chapterVersionPOMapper;
    @Resource
    private TenantSubscribePOMapper tenantSubscribePOMapper;
    @Resource
    private TenantChannelPOMapper tenantChannelPOMapper;
    @Resource
    private QuestionService  questionService;
    @Resource
    private BookVersionPOMapper bookVersionPOMapper;
    @Resource
    private BookVersionChapterVersionRelationPOMapper bookVersionChapterVersionRelationPOMapper;

    @Bean
    public LoadingCache<Long, ChapterNodeStructure> chapterNodeCache() {
        return Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(Duration.ofDays(3))
                .build(this::getNodeStructureByChapterVersionId);
    }

    @Bean
    public LoadingCache<QuestionId, BigQuestionGroup> questionForJudgeCache() {
        return Caffeine.newBuilder()
                .maximumSize(10_000)
                .expireAfterWrite(Duration.ofDays(3))
                .build(questionId -> {
                    BigQuestionGroup bigQuestion = questionService.getBigQuestion(questionId.getBizQuestionId(), questionId.getVersionNumber());
                    return processQuestionForJudge(bigQuestion);
                });
    }

    @Bean
    public LoadingCache<TenantSubscribeID, TenantSubscribePO> tenantSubscribeCache() {
        return Caffeine.newBuilder()
                .maximumSize(500)
                .expireAfterWrite(Duration.ofHours(1))
                .build(tenantSubscribeID -> {
                    return tenantSubscribePOMapper.selectByTenantIdAndMessageTopic(tenantSubscribeID.tenantId(), tenantSubscribeID.messageTopic());
                });
    }

    @Bean
    public LoadingCache<TenantSubscribeID, Map<Integer, TenantChannelPO>> tenantChannelCache() {
        return Caffeine.newBuilder()
                .maximumSize(500)
                .expireAfterWrite(Duration.ofHours(1))
                .build(tenantSubscribeID -> {
                    List<TenantChannelPO> tenantChannelPOS = tenantChannelPOMapper.selectByTenantIdAndMessageTopic(tenantSubscribeID.tenantId(), tenantSubscribeID.messageTopic());
                    if (tenantChannelPOS == null || tenantChannelPOS.isEmpty()) {
                        return Collections.emptyMap();
                    }
                    return tenantChannelPOS.stream().collect(Collectors.toMap(TenantChannelPO::getChannel, tenantChannelPO -> tenantChannelPO, (existing, replacement) -> existing));
                });
    }

    @Bean
    public LoadingCache<BookID, BookVersion> bookVersionCache() {
        return Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(Duration.ofHours(3))
                .build(bookID -> {
                    BookVersionPO bookVersionPO = bookVersionPOMapper.selectByBookIdAndVersionNum(bookID.bookId(), bookID.bookVersionNumber());
                    if (bookVersionPO == null) {
                        return null;
                    }
                    BookVersion bookVersion = bookVersionPO.toEntity();
                    List<BookVersionChapterVersionRelationPO> chapterVersionIds = bookVersionChapterVersionRelationPOMapper.selectByBookVersionId(bookVersionPO.getId());
                    if (chapterVersionIds == null || chapterVersionIds.isEmpty()) {
                        return bookVersion;
                    }
                    bookVersion.setChapterVersionIdList(chapterVersionIds.stream().map(BookVersionChapterVersionRelationPO::getChapterVersionId).toList());
                    return bookVersion;
                });
    }

    private BigQuestionGroup processQuestionForJudge(BigQuestionGroup bigQuestion) {
        if (bigQuestion == null) {
            return null;
        }
        bigQuestion.setSetting(null);
        bigQuestion.setContent(null);
        bigQuestion.setUpdateBy(null);
        bigQuestion.setCreateBy(null);
        bigQuestion.setUpdateTime(null);
        bigQuestion.setCreateTime(null);
        bigQuestion.setDirection(null);
        List<Question> questions = bigQuestion.getQuestions();
        questions.forEach(this::questionPeek);
        return bigQuestion;
    }

    private void questionPeek(Question question) {
        question.setContent(null);
        question.setUpdateBy(null);
        question.setCreateBy(null);
        question.setUpdateTime(null);
        question.setCreateTime(null);
        question.setDirection(null);
        questionTextPeek(question.getQuestionText());
        optionsPeek(question.getOptions());
        answerPeek(question.getAnswers());
        if (CollectionUtils.isEmpty(question.getQuestions())) {
            return;
        }
        question.getQuestions().forEach(this::questionPeek);
    }

    private void questionTextPeek(QuestionText questionText) {
        questionText.setText(null);
        questionText.setRelevancy(null);
        questionText.setMedia(null);
        questionText.setPhoneticSymbol(null);
        questionText.setAnswerWordLimit(null);
        questionText.setPrepareTime(null);
        questionText.setAnswerTime(null);
        if (questionText.getOptions() != null) {
            questionText.getOptions().forEach(option -> {
                option.setContent(null);
                option.setCreateBy(null);
                option.setUpdateBy(null);
            });
        }

    }

    private void answerPeek(List<QuestionAnswer> questionAnswers) {
        if (CollectionUtils.isEmpty(questionAnswers)) {
            return;
        }
        questionAnswers.forEach(questionAnswer -> {
            questionAnswer.setCreateBy(null);
            questionAnswer.setUpdateBy(null);
        });
    }
    private void optionsPeek(List<ChoiceQuestionOption> options) {
        if (CollectionUtils.isEmpty(options)) {
            return;
        }
        options.forEach(option -> {
            option.setContent(null);
            option.setCreateBy(null);
            option.setUpdateBy(null);
        });
    }
    private ChapterNodeStructure getNodeStructureByChapterVersionId(Long chapterVersionId) {
        ChapterVersionPO chapterVersionPO = chapterVersionPOMapper.selectChapterNodeByChapterVersionId(chapterVersionId);
        if (chapterVersionPO == null || CollectionUtils.isEmpty(chapterVersionPO.getTotalStruct())) {
            return null;
        }
        List<ChapterNodePO> totalStruct = chapterVersionPO.getTotalStruct();
        if (CollectionUtils.isEmpty(totalStruct)) {
            return null;
        }
        List<ChapterNode> entityList = totalStruct.stream()
                .map(ChapterNodePO::toEntity)
                .toList();
        List<ChapterNode> roots = ChapterNode.buildTree(entityList);
        roots.forEach(root -> ChapterNode.calculateTotalNonHDescendants(root, 0));
        Map<String, ChapterNode> nodeMap = new LinkedHashMap<>();
        Map<String, ChapterNode> questionNodeMap = new LinkedHashMap<>();
        int totalNonHNodeCount = 0;
        for (int i = 0; i < entityList.size(); i++) {
            ChapterNode entity = entityList.get(i);
            entity.setOffset(i);
            String questionType = entity.getQuestionType();
            if (!StringUtils.hasText(questionType)) {
                entity.setText(null);
            } else {
                questionNodeMap.put(entity.getText(), entity);
            }
            if (!entity.isHeader()) {
                totalNonHNodeCount++;
            }
            nodeMap.put(entity.getId(), entity);
        }
        ChapterNodeStructure nodeStructure = new ChapterNodeStructure();
        nodeStructure.setNodeMap(nodeMap);
        nodeStructure.setTotalNonHNodeCount(totalNonHNodeCount);
        nodeStructure.setQuestionNodeMap(questionNodeMap);
        return nodeStructure;
    }

}
