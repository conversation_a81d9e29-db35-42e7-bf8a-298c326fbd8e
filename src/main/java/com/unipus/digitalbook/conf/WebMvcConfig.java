package com.unipus.digitalbook.conf;

import com.unipus.digitalbook.aop.auth.AuthInterceptor;
import com.unipus.digitalbook.aop.bussinese.ActionValidInterceptor;
import com.unipus.digitalbook.aop.ipaddress.InternalIpInterceptor;
import com.unipus.digitalbook.aop.permission.PermissionInterceptor;
import com.unipus.digitalbook.common.utils.ArrayUtil;
import com.unipus.digitalbook.conf.security.WhiteListProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    private final AuthInterceptor authInterceptor;
    private final PermissionInterceptor permissionInterceptor;
    private final InternalIpInterceptor internalIpInterceptor;
    private final ActionValidInterceptor actionValidInterceptor;

    private final String[] securityWhiteList;
    private final String[] permissionWhiteList;
    private final String[] internalIpWhiteList;

    public WebMvcConfig(
            AuthInterceptor authInterceptor,
            PermissionInterceptor permissionInterceptor,
            InternalIpInterceptor internalIpInterceptor,
            WhiteListProperties whiteList,
            ActionValidInterceptor actionValidInterceptor) {
        this.authInterceptor = authInterceptor;
        this.permissionInterceptor = permissionInterceptor;
        this.internalIpInterceptor = internalIpInterceptor;
        this.actionValidInterceptor = actionValidInterceptor;

        // 认证白名单URL
        this.securityWhiteList = ArrayUtil.mergeAllArrays(
                whiteList.getSecurityUrls(),
                whiteList.getInternalUrls());
        // 权限白名单URL
        this.permissionWhiteList = ArrayUtil.mergeAllArrays(
                whiteList.getSecurityUrls(),
                whiteList.getPermissionUrls(),
                whiteList.getInternalUrls());
        // 内部IP白名单URL
        this.internalIpWhiteList = whiteList.getInternalUrls().length == 0 ?
                new String[]{"/__no_match__"} : whiteList.getInternalUrls();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加请求日志拦截器
//        registry.addInterceptor(requestLoggingInterceptor)
//                .order(2)
//                // 拦截所有请求
//                .addPathPatterns("/**");

        // 添加JWT拦截器
        registry.addInterceptor(authInterceptor)
                .order(3)
                // 拦截所有路径的请求
                .addPathPatterns("/**")
                .excludePathPatterns(this.securityWhiteList);

        // 添加权限拦截器
        registry.addInterceptor(permissionInterceptor)
                .order(4)
                // 拦截所有路径的请求
                .addPathPatterns("/**")
                .excludePathPatterns(this.permissionWhiteList);

        // 添加内部访问拦截器
        registry.addInterceptor(internalIpInterceptor)
                .order(5)
                .addPathPatterns(this.internalIpWhiteList);


        // 添加用户行为拦截器
        registry.addInterceptor(actionValidInterceptor)
                .order(6)
                .addPathPatterns("/reader/action/**")
                .addPathPatterns("/reader/customContent/action/**");

    }
}
