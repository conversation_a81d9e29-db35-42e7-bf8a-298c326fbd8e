package com.unipus.digitalbook.model.params.action;

import com.unipus.digitalbook.model.entity.chapter.ChapterNode;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class NodeListDTO implements Serializable {
    /**
     * 已完成的节点列表
     */
    private List<NodeDTO> nodeList;

    public NodeListDTO() {
    }
    public NodeListDTO(Collection<ChapterNode> chapterNodes) {
        this.nodeList = chapterNodes.parallelStream().map(c -> {
            NodeDTO nodeDTO = new NodeDTO();
            nodeDTO.setId(c.getId());
            nodeDTO.setText(c.getText());
            nodeDTO.setType(c.getType());
            nodeDTO.setOffset(c.getOffset());
            return nodeDTO;
        }).toList();
    }

    public NodeListDTO(Collection<ChapterNode> chapterNodes, Map<String, Integer> nodeCompletedCount) {
        this.nodeList = chapterNodes.parallelStream().map(c -> {
            NodeDTO nodeDTO = new NodeDTO();
            nodeDTO.setId(c.getId());
            nodeDTO.setText(c.getText());
            nodeDTO.setType(c.getType());
            nodeDTO.setOffset(c.getOffset());
            Integer count = nodeCompletedCount.getOrDefault(c.getId(), 0);
            if (c.isHeader()) {
                nodeDTO.setFinished(count >= c.getTotalNonHDescendants());
            } else {
                nodeDTO.setFinished(count >= 1);
            }
            return nodeDTO;
        }).toList();
    }

    public List<NodeDTO> getNodeList() {
        return nodeList;
    }

    public void setNodeList(List<NodeDTO> nodeList) {
        this.nodeList = nodeList;
    }
}
