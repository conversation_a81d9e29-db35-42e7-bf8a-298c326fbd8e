package com.unipus.digitalbook.model.entity.wordpractice;

import com.unipus.digitalbook.model.po.WordPracticeInstancePO;
import com.unipus.digitalbook.model.po.WordPracticePO;
import com.unipus.digitalbook.model.po.chapter.ChapterPO;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class WordPracticeList implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private List<ChapterPO> chapterPOList;

    private List<WordPracticePO> wordPracticePOList;

    private List<WordPracticeInstancePO> wordPracticeInstancePOList;

    public WordPracticeList(List<ChapterPO> chapterPOList, List<WordPracticePO> wordPracticePOList, List<WordPracticeInstancePO> wordPracticeInstancePOList) {
        this.chapterPOList = chapterPOList;
        this.wordPracticePOList = wordPracticePOList;
        this.wordPracticeInstancePOList = wordPracticeInstancePOList;
    }
}
