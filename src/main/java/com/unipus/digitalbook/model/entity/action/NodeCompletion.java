package com.unipus.digitalbook.model.entity.action;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

public class NodeCompletion implements Serializable {
    /**
     * 节点完成情况
     */
    private Map<String, Integer> nodeCompletedCount;

    public NodeCompletion() {}

    public NodeCompletion(Map<String, Integer> nodeCompletedCount) {
        this.nodeCompletedCount = nodeCompletedCount;
    }

    public Map<String, Integer> getNodeCompletedCount() {
        return nodeCompletedCount;
    }

    public void setNodeCompletedCount(Map<String, Integer> nodeCompletedCount) {
        this.nodeCompletedCount = nodeCompletedCount;
    }
}
