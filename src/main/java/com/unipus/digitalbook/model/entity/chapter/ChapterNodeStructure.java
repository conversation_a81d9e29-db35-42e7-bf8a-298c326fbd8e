package com.unipus.digitalbook.model.entity.chapter;

import java.util.LinkedHashMap;
import java.util.Map;

public class ChapterNodeStructure {
    /**
     * 节点索引
     */
    private Map<String, ChapterNode> nodeMap;

    /**
     * 题目节点索引
     * key题目id
     */
    private Map<String, ChapterNode> questionNodeMap;
    /**
     * 非H节点数量
     */
    private int totalNonHNodeCount;

    public Map<String, ChapterNode> getNodeMap() {
        return nodeMap;
    }

    public void setNodeMap(Map<String, ChapterNode> nodeMap) {
        this.nodeMap = nodeMap;
    }

    public int getTotalNonHNodeCount() {
        return totalNonHNodeCount;
    }

    public void setTotalNonHNodeCount(int totalNonHNodeCount) {
        this.totalNonHNodeCount = totalNonHNodeCount;
    }

    public Map<String, ChapterNode> getQuestionNodeMap() {
        return questionNodeMap;
    }

    public void setQuestionNodeMap(Map<String, ChapterNode> questionNodeMap) {
        this.questionNodeMap = questionNodeMap;
    }
}
