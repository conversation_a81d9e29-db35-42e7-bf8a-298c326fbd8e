package com.unipus.digitalbook.model.entity.wordpractice;

import com.unipus.digitalbook.model.po.WordPracticePO;
import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class WordPracticeSearchList implements java.io.Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 列表
     */
    private transient List<WordPracticePO> wordPracticeList;

    /**
     * 总数
     */
    private Integer totalCount;

}
