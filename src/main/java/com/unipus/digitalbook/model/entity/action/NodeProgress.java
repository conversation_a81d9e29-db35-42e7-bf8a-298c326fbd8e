package com.unipus.digitalbook.model.entity.action;

public class NodeProgress {
        /**
         * 节点ID
         */
        private String id;
        /**
         * 节点类型
         */
        private String type;

        /**
         * 节点状态 1 完成
         */
        private Integer status;

        /**
         * 节点总数量
         */
        private int total;

        /**
         * 节点完成数量
         */
        private int completed;

        /**
         * 是否是直接节点
         */
        private boolean directNode;

        public NodeProgress() {}

        public NodeProgress(String id, String type, int total, int completed) {
            this.id = id;
            this.type = type;
            this.total = total;
            this.completed = completed;
            this.status = completed >= total ? 1 : 0;
        }

        public NodeProgress(String id, String type, int total, int completed, boolean directNode) {
            this(id, type, total, completed);
            this.directNode = directNode;
        }
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }


        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        public int getCompleted() {
            return completed;
        }

        public void setCompleted(int completed) {
            this.completed = completed;
        }

        public boolean isDirectNode() {
            return directNode;
        }

        public void setDirectNode(boolean directNode) {
            this.directNode = directNode;
        }
}