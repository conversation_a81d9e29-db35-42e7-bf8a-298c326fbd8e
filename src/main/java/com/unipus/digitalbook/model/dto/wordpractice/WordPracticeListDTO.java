package com.unipus.digitalbook.model.dto.wordpractice;

import com.unipus.digitalbook.model.dto.chapter.ChapterWordPracticeDTO;
import com.unipus.digitalbook.model.entity.wordpractice.WordPracticeList;
import com.unipus.digitalbook.model.po.WordPracticeInstancePO;
import com.unipus.digitalbook.model.po.WordPracticePO;
import com.unipus.digitalbook.model.po.chapter.ChapterPO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@Schema(description = "词汇学练DTO")
@NoArgsConstructor
public class WordPracticeListDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "章节列表")
    private List<ChapterWordPracticeDTO> chapterList;

    public WordPracticeListDTO(WordPracticeList wpList)  {
        chapterList = new ArrayList<>();
        wpList.getChapterPOList().forEach(chapterPO -> {
            ChapterWordPracticeDTO chapterWordPracticeDTO = new ChapterWordPracticeDTO();
            chapterWordPracticeDTO.setId(chapterPO.getId());
            chapterWordPracticeDTO.setChapterNumber(chapterPO.getChapterNumber());
            chapterWordPracticeDTO.setName(chapterPO.getName());

            List<WordPracticeListItemDTO> wordPracticeList = wpList.getWordPracticePOList().stream().filter(wpp -> Objects.equals(wpp.getParentId(), chapterPO.getId())).map(wpp -> {
                WordPracticeListItemDTO wordPracticeListItemDTO = new WordPracticeListItemDTO();
                wordPracticeListItemDTO.setId(wpp.getId());
                wordPracticeListItemDTO.setName(wpp.getName());
                wpList.getWordPracticeInstancePOList().stream()
                        .filter(wppi -> Objects.equals(wppi.getStatus(), 1) && Objects.equals(wppi.getPracticeId(), wpp.getId()))
                        .findFirst()
                        .ifPresent(wppi -> wordPracticeListItemDTO.setPublishStatusId(wppi.getInstanceId()));
                return wordPracticeListItemDTO;
            }).toList();
            chapterWordPracticeDTO.setWordPracticeList(wordPracticeList);

            chapterList.add(chapterWordPracticeDTO);
        });
    }
}
