package com.unipus.digitalbook.model.dto.wordpractice;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Schema(description = "词汇学练DTO")
public class WordPracticeDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @Schema(description = "ID")
    private Long id;
    /**
     * 业务ID
     */
    @Schema(description = "业务ID")
    private String bizId;
    /**
     * 教材名称
     */
    @Schema(description = "教材名称")
    private String bookName;
    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;
    /**
     * 状态 0未发布 1已发布
     */
    @Schema(description = "状态 0未发布 1已发布")
    private Integer status;
    /**
     * 内容数据json
     */
    @Schema(description = "内容数据json")
    private String jsonData;

}
