package com.unipus.digitalbook.model.dto.wordpractice;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@Schema(description = "词汇学练DTO")
@NoArgsConstructor
public class WordPracticeListItemDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，唯一标识一条词汇学练记录
     */
    @Schema(description = "主键ID")
    private Long id;
    /**
     * 发布态id
     */
    @Schema(description = "发布态的词汇学练id")
    private String publishStatusId;
    /**
     * 词汇学练的名称
     */
    @Schema(description = "词汇学练的名称")
    private String name;
}
