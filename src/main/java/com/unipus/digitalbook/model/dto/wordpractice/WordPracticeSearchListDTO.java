package com.unipus.digitalbook.model.dto.wordpractice;

import com.unipus.digitalbook.model.entity.wordpractice.WordPractice;
import com.unipus.digitalbook.model.entity.wordpractice.WordPracticeSearchList;
import com.unipus.digitalbook.model.po.WordPracticePO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class WordPracticeSearchListDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 列表数据
     */
    @Schema(description = "列表数据")
    private List<WordPractice> wordPracticeList;

    /**
     * 数据总数
     */
    @Schema(description = "数据总数")
    private Integer totalCount;


    public WordPracticeSearchListDTO(WordPracticeSearchList wordPracticeSearchList) {
        this.wordPracticeList = wordPracticeSearchList.getWordPracticeList().stream().map(WordPracticePO::toEntity).toList();
        this.totalCount = wordPracticeSearchList.getTotalCount();
    }

}
