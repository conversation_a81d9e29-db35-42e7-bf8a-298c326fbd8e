package com.unipus.digitalbook.model.po;


import com.unipus.digitalbook.model.entity.wordpractice.WordPractice;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 词汇学练持久化对象，用于与数据库表进行映射
 */
@Data
@NoArgsConstructor
public class WordPracticePO {
    /**
     * 主键ID，唯一标识一条词汇学练记录
     */
    private Long id;
    /**
     * 业务ID
     */
    private String bizId;
    /**
     * 上级ID
     */
    private String parentId;
    /**
     * 词汇学练的名称
     */
    private String name;

    /**
     * 记录创建的时间
     */
    private Date createTime;

    /**
     * 记录最后更新的时间
     */
    private Date updateTime;

    /**
     * 创建该记录的用户ID
     */
    private Long createBy;

    /**
     * 最后更新该记录的用户ID
     */
    private Long updateBy;

    /**
     * 记录是否有效，0 表示无效，1 表示有效
     */
    private Boolean enable;

    /**
     * 创建用户名称
     */
    private String createUserName;

    /**
     * 将持久化对象转换为实体对象
     * @return 词汇学练实体对象
     */
    public WordPractice toEntity() {
        WordPractice entity = new WordPractice();
        entity.setId(this.id);
        entity.setBizId(this.bizId);
        entity.setName(this.name);
        entity.setCreateTime(this.createTime);
        entity.setUpdateTime(this.updateTime);
        entity.setCreateBy(this.createBy);
        entity.setUpdateBy(this.updateBy);
        entity.setEnable(this.enable);
        entity.setCreateUserName(this.createUserName);
        return entity;
    }
}
